import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../models/errand.dart';

class ActiveErrandsScreen extends StatefulWidget {
  const ActiveErrandsScreen({super.key});

  @override
  State<ActiveErrandsScreen> createState() => _ActiveErrandsScreenState();
}

class _ActiveErrandsScreenState extends State<ActiveErrandsScreen> {
  bool _isLoading = true;
  List<ActiveErrand> _activeErrands = [];
  List<ActiveErrand> _completedErrands = [];

  @override
  void initState() {
    super.initState();
    _loadActiveErrands();
  }

  Future<void> _loadActiveErrands() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // In a real app, this would be an API call
      final errands = await _getMockActiveErrands();
      
      setState(() {
        _activeErrands = errands.where((e) => 
            e.status == ErrandStatus.accepted || 
            e.status == ErrandStatus.inProgress).toList();
        _completedErrands = errands.where((e) => 
            e.status == ErrandStatus.completed).toList();
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to load errands: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<List<ActiveErrand>> _getMockActiveErrands() async {
    // Simulate API delay
    await Future.delayed(const Duration(milliseconds: 800));
    
    return [
      ActiveErrand(
        id: 1,
        title: 'House Cleaning - Upper East Side',
        customerName: 'Sarah Johnson',
        customerPhone: '******-567-8901',
        customerAvatar: null,
        location: '123 Fifth Avenue, New York, NY 10003',
        scheduledDate: DateTime.now().add(const Duration(hours: 2)),
        payment: 85.0,
        status: ErrandStatus.accepted,
        description: 'Complete house cleaning including all rooms, kitchen, and bathrooms. Please bring your own cleaning supplies.',
        estimatedDuration: const Duration(hours: 3),
        specialInstructions: 'Please focus on kitchen and bathrooms. Keys will be left with doorman.',
        customerRating: 4.8,
        distanceFromRunner: 2.3,
        statusHistory: [
          ErrandStatusUpdate(
            status: ErrandStatus.accepted,
            timestamp: DateTime.now().subtract(const Duration(hours: 1)),
            note: 'Job accepted',
          ),
        ],
      ),
      ActiveErrand(
        id: 2,
        title: 'Grocery Shopping - Brooklyn',
        customerName: 'Mike Wilson',
        customerPhone: '******-678-9012',
        customerAvatar: null,
        location: '456 Park Slope Ave, Brooklyn, NY 11215',
        scheduledDate: DateTime.now().add(const Duration(minutes: 30)),
        payment: 45.0,
        status: ErrandStatus.inProgress,
        description: 'Weekly grocery shopping at Whole Foods. Shopping list attached.',
        estimatedDuration: const Duration(hours: 2),
        specialInstructions: 'Customer prefers organic products. Call if any items are unavailable.',
        customerRating: 4.9,
        distanceFromRunner: 1.8,
        statusHistory: [
          ErrandStatusUpdate(
            status: ErrandStatus.accepted,
            timestamp: DateTime.now().subtract(const Duration(hours: 2)),
            note: 'Job accepted',
          ),
          ErrandStatusUpdate(
            status: ErrandStatus.inProgress,
            timestamp: DateTime.now().subtract(const Duration(minutes: 15)),
            note: 'Started grocery shopping',
          ),
        ],
      ),
      ActiveErrand(
        id: 3,
        title: 'Furniture Assembly - Manhattan',
        customerName: 'Emily Davis',
        customerPhone: '******-789-0123',
        customerAvatar: null,
        location: '789 Broadway, New York, NY 10003',
        scheduledDate: DateTime.now().subtract(const Duration(hours: 2)),
        payment: 120.0,
        status: ErrandStatus.completed,
        description: 'Assemble IKEA bedroom furniture - bed frame, dresser, and nightstand.',
        estimatedDuration: const Duration(hours: 4),
        specialInstructions: 'All tools provided. Apartment is on 5th floor with elevator.',
        customerRating: 4.7,
        distanceFromRunner: 3.1,
        completedDate: DateTime.now().subtract(const Duration(minutes: 30)),
        statusHistory: [
          ErrandStatusUpdate(
            status: ErrandStatus.accepted,
            timestamp: DateTime.now().subtract(const Duration(hours: 5)),
            note: 'Job accepted',
          ),
          ErrandStatusUpdate(
            status: ErrandStatus.inProgress,
            timestamp: DateTime.now().subtract(const Duration(hours: 3)),
            note: 'Arrived at location and started assembly',
          ),
          ErrandStatusUpdate(
            status: ErrandStatus.completed,
            timestamp: DateTime.now().subtract(const Duration(minutes: 30)),
            note: 'All furniture assembled successfully',
          ),
        ],
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 2,
      child: Scaffold(
        backgroundColor: Colors.grey.shade50,
        appBar: AppBar(
          title: const Text('Active Jobs'),
          backgroundColor: const Color(0xFF1B365D),
          foregroundColor: Colors.white,
          actions: [
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: _loadActiveErrands,
            ),
          ],
          bottom: TabBar(
            labelColor: Colors.white,
            unselectedLabelColor: Colors.white70,
            indicatorColor: Colors.white,
            tabs: [
              Tab(text: 'Active (${_activeErrands.length})'),
              Tab(text: 'Completed (${_completedErrands.length})'),
            ],
          ),
        ),
        body: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : TabBarView(
                children: [
                  _buildActiveErrandsTab(),
                  _buildCompletedErrandsTab(),
                ],
              ),
      ),
    );
  }

  Widget _buildActiveErrandsTab() {
    if (_activeErrands.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.work_outline,
              size: 80,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 24),
            const Text(
              'No Active Jobs',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Color(0xFF1B365D),
              ),
            ),
            const SizedBox(height: 12),
            Text(
              'Check the available jobs to start earning',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 32),
            ElevatedButton(
              onPressed: () => context.go('/runner/errands'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF1B365D),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
              ),
              child: const Text('Browse Available Jobs'),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadActiveErrands,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _activeErrands.length,
        itemBuilder: (context, index) {
          final errand = _activeErrands[index];
          return _buildActiveErrandCard(errand);
        },
      ),
    );
  }

  Widget _buildCompletedErrandsTab() {
    if (_completedErrands.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.history,
              size: 80,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 24),
            const Text(
              'No Completed Jobs',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Color(0xFF1B365D),
              ),
            ),
            const SizedBox(height: 12),
            Text(
              'Completed jobs will appear here',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _completedErrands.length,
      itemBuilder: (context, index) {
        final errand = _completedErrands[index];
        return _buildCompletedErrandCard(errand);
      },
    );
  }

  Widget _buildActiveErrandCard(ActiveErrand errand) {
    final isUrgent = errand.scheduledDate.difference(DateTime.now()).inHours < 1;
    
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isUrgent ? Colors.orange.shade300 : Colors.grey.shade200,
          width: isUrgent ? 2 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with status
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: _getStatusColor(errand.status).withOpacity(0.1),
              borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: _getStatusColor(errand.status),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    _getStatusIcon(errand.status),
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        errand.title,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF1B365D),
                        ),
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: _getStatusColor(errand.status),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              _getStatusText(errand.status),
                              style: const TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                                color: Colors.white,
                              ),
                            ),
                          ),
                          if (isUrgent) ...[
                            const SizedBox(width: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: Colors.orange,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: const Text(
                                'URGENT',
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                    ],
                  ),
                ),
                Text(
                  '\$${errand.payment.toStringAsFixed(0)}',
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
              ],
            ),
          ),
          
          // Customer info and details
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    CircleAvatar(
                      radius: 20,
                      backgroundColor: const Color(0xFF1B365D),
                      child: Text(
                        errand.customerName[0],
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            errand.customerName,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          Row(
                            children: [
                              const Icon(Icons.star, color: Colors.amber, size: 16),
                              const SizedBox(width: 4),
                              Text(
                                errand.customerRating.toStringAsFixed(1),
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey.shade600,
                                ),
                              ),
                              const SizedBox(width: 16),
                              const Icon(Icons.location_on, color: Colors.grey, size: 16),
                              const SizedBox(width: 4),
                              Text(
                                '${errand.distanceFromRunner.toStringAsFixed(1)} miles',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey.shade600,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        IconButton(
                          onPressed: () => _callCustomer(errand),
                          icon: const Icon(Icons.phone),
                          style: IconButton.styleFrom(
                            backgroundColor: Colors.green.shade50,
                            foregroundColor: Colors.green,
                          ),
                        ),
                        const SizedBox(width: 8),
                        IconButton(
                          onPressed: () => _messageCustomer(errand),
                          icon: const Icon(Icons.message),
                          style: IconButton.styleFrom(
                            backgroundColor: Colors.blue.shade50,
                            foregroundColor: Colors.blue,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                
                const SizedBox(height: 16),
                
                // Schedule and location info
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade50,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          const Icon(Icons.schedule, size: 16, color: Colors.grey),
                          const SizedBox(width: 8),
                          Text(
                            'Scheduled: ${_formatDateTime(errand.scheduledDate)}',
                            style: const TextStyle(fontSize: 14),
                          ),
                          const Spacer(),
                          Text(
                            '~${_formatDuration(errand.estimatedDuration)}',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey.shade600,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Icon(Icons.location_on, size: 16, color: Colors.grey),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              errand.location,
                              style: const TextStyle(fontSize: 14),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                
                if (errand.specialInstructions.isNotEmpty) ...[
                  const SizedBox(height: 12),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade50,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.blue.shade200),
                    ),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Icon(Icons.info_outline, size: 16, color: Colors.blue.shade700),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            errand.specialInstructions,
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.blue.shade700,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
                
                const SizedBox(height: 16),
                
                // Action buttons
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () => _showErrandDetails(errand),
                        icon: const Icon(Icons.info_outline),
                        label: const Text('Details'),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: const Color(0xFF1B365D),
                          side: const BorderSide(color: Color(0xFF1B365D)),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () => _updateErrandStatus(errand),
                        icon: Icon(_getNextStatusIcon(errand.status)),
                        label: Text(_getNextStatusAction(errand.status)),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: _getStatusColor(errand.status),
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCompletedErrandCard(ActiveErrand errand) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.green,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.check_circle,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      errand.title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF1B365D),
                      ),
                    ),
                    Text(
                      'Completed ${_formatDate(errand.completedDate!)}',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
              Text(
                '+\$${errand.payment.toStringAsFixed(0)}',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.green,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              CircleAvatar(
                radius: 16,
                backgroundColor: const Color(0xFF1B365D),
                child: Text(
                  errand.customerName[0],
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Text(
                errand.customerName,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              TextButton(
                onPressed: () => _showErrandDetails(errand),
                child: const Text('View Details'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(ErrandStatus status) {
    switch (status) {
      case ErrandStatus.accepted:
        return Colors.blue;
      case ErrandStatus.inProgress:
        return Colors.orange;
      case ErrandStatus.completed:
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon(ErrandStatus status) {
    switch (status) {
      case ErrandStatus.accepted:
        return Icons.assignment_turned_in;
      case ErrandStatus.inProgress:
        return Icons.work;
      case ErrandStatus.completed:
        return Icons.check_circle;
      default:
        return Icons.help;
    }
  }

  String _getStatusText(ErrandStatus status) {
    switch (status) {
      case ErrandStatus.accepted:
        return 'Accepted';
      case ErrandStatus.inProgress:
        return 'In Progress';
      case ErrandStatus.completed:
        return 'Completed';
      default:
        return 'Unknown';
    }
  }

  IconData _getNextStatusIcon(ErrandStatus status) {
    switch (status) {
      case ErrandStatus.accepted:
        return Icons.play_arrow;
      case ErrandStatus.inProgress:
        return Icons.check;
      default:
        return Icons.info;
    }
  }

  String _getNextStatusAction(ErrandStatus status) {
    switch (status) {
      case ErrandStatus.accepted:
        return 'Start Job';
      case ErrandStatus.inProgress:
        return 'Complete';
      default:
        return 'View';
    }
  }

  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = dateTime.difference(now);
    
    if (difference.inDays == 0) {
      final hour = dateTime.hour > 12 ? dateTime.hour - 12 : (dateTime.hour == 0 ? 12 : dateTime.hour);
      final minute = dateTime.minute.toString().padLeft(2, '0');
      final period = dateTime.hour >= 12 ? 'PM' : 'AM';
      
      if (difference.inHours < 1 && difference.inMinutes > 0) {
        return 'In ${difference.inMinutes} minutes';
      } else if (difference.inHours == 0 && difference.inMinutes <= 0) {
        return 'Now';
      }
      return 'Today at $hour:$minute $period';
    } else if (difference.inDays == 1) {
      return 'Tomorrow';
    } else {
      return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date).inDays;
    
    if (difference == 0) {
      return 'today';
    } else if (difference == 1) {
      return 'yesterday';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  String _formatDuration(Duration duration) {
    if (duration.inHours > 0) {
      return '${duration.inHours}h';
    } else {
      return '${duration.inMinutes}m';
    }
  }

  void _callCustomer(ActiveErrand errand) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Calling ${errand.customerName}...')),
    );
  }

  void _messageCustomer(ActiveErrand errand) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Opening chat feature coming soon')),
    );
  }

  void _updateErrandStatus(ActiveErrand errand) {
    showDialog(
      context: context,
      builder: (context) => _StatusUpdateDialog(
        errand: errand,
        onStatusUpdate: (newStatus, note) async {
          try {
            // In real app, update status via API
            await Future.delayed(const Duration(seconds: 1));
            
            setState(() {
              final index = _activeErrands.indexWhere((e) => e.id == errand.id);
              if (index >= 0) {
                _activeErrands[index] = errand.copyWith(
                  status: newStatus,
                  completedDate: newStatus == ErrandStatus.completed ? DateTime.now() : null,
                );
                _activeErrands[index].statusHistory.add(
                  ErrandStatusUpdate(
                    status: newStatus,
                    timestamp: DateTime.now(),
                    note: note,
                  ),
                );
                
                if (newStatus == ErrandStatus.completed) {
                  _completedErrands.insert(0, _activeErrands.removeAt(index));
                }
              }
            });
            
            Navigator.of(context).pop();
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Job status updated to ${_getStatusText(newStatus)}'),
                backgroundColor: Colors.green,
              ),
            );
          } catch (e) {
            Navigator.of(context).pop();
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Failed to update status: $e'),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
      ),
    );
  }

  void _showErrandDetails(ActiveErrand errand) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.8,
        maxChildSize: 0.95,
        minChildSize: 0.5,
        builder: (context, scrollController) {
          return Container(
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
            ),
            child: Column(
              children: [
                Container(
                  margin: const EdgeInsets.only(top: 8),
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.grey.shade300,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(20),
                  child: Row(
                    children: [
                      const Text(
                        'Job Details',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF1B365D),
                        ),
                      ),
                      const Spacer(),
                      IconButton(
                        onPressed: () => Navigator.of(context).pop(),
                        icon: const Icon(Icons.close),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: SingleChildScrollView(
                    controller: scrollController,
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: _buildErrandDetailsContent(errand),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildErrandDetailsContent(ActiveErrand errand) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Status and payment
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: _getStatusColor(errand.status).withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            children: [
              Icon(
                _getStatusIcon(errand.status),
                color: _getStatusColor(errand.status),
                size: 24,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      errand.title,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF1B365D),
                      ),
                    ),
                    Text(
                      _getStatusText(errand.status),
                      style: TextStyle(
                        fontSize: 14,
                        color: _getStatusColor(errand.status),
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
              Text(
                '\$${errand.payment.toStringAsFixed(0)}',
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.green,
                ),
              ),
            ],
          ),
        ),
        
        const SizedBox(height: 24),
        
        // Customer information
        _buildDetailSection(
          'Customer',
          [
            _buildDetailRow('Name', errand.customerName),
            _buildDetailRow('Phone', errand.customerPhone),
            _buildDetailRow('Rating', '${errand.customerRating} ⭐'),
          ],
        ),
        
        const SizedBox(height: 24),
        
        // Job information
        _buildDetailSection(
          'Job Information',
          [
            _buildDetailRow('Scheduled', _formatDateTime(errand.scheduledDate)),
            _buildDetailRow('Duration', _formatDuration(errand.estimatedDuration)),
            _buildDetailRow('Distance', '${errand.distanceFromRunner.toStringAsFixed(1)} miles'),
            if (errand.completedDate != null)
              _buildDetailRow('Completed', _formatDate(errand.completedDate!)),
          ],
        ),
        
        const SizedBox(height: 24),
        
        // Description
        _buildDetailSection(
          'Description',
          [
            Text(
              errand.description,
              style: const TextStyle(fontSize: 16, height: 1.5),
            ),
          ],
        ),
        
        if (errand.specialInstructions.isNotEmpty) ...[
          const SizedBox(height: 24),
          _buildDetailSection(
            'Special Instructions',
            [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue.shade200),
                ),
                child: Text(
                  errand.specialInstructions,
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.blue.shade700,
                    height: 1.5,
                  ),
                ),
              ),
            ],
          ),
        ],
        
        const SizedBox(height: 24),
        
        // Location
        _buildDetailSection(
          'Location',
          [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Icon(Icons.location_on, color: Colors.red, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    errand.location,
                    style: const TextStyle(fontSize: 16, height: 1.5),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            ElevatedButton.icon(
              onPressed: () => _openInMaps(errand.location),
              icon: const Icon(Icons.navigation),
              label: const Text('Open in Maps'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 24),
        
        // Status history
        if (errand.statusHistory.isNotEmpty) ...[
          _buildDetailSection(
            'Status History',
            errand.statusHistory.map((update) => Container(
              margin: const EdgeInsets.only(bottom: 8),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    _getStatusIcon(update.status),
                    color: _getStatusColor(update.status),
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          update.note,
                          style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
                        ),
                        Text(
                          _formatDateTime(update.timestamp),
                          style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            )).toList(),
          ),
        ],
        
        const SizedBox(height: 40),
      ],
    );
  }

  Widget _buildDetailSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Color(0xFF1B365D),
          ),
        ),
        const SizedBox(height: 12),
        ...children,
      ],
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade600,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _openInMaps(String location) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Opening $location in maps...')),
    );
  }
}

class _StatusUpdateDialog extends StatefulWidget {
  final ActiveErrand errand;
  final Function(ErrandStatus status, String note) onStatusUpdate;

  const _StatusUpdateDialog({
    required this.errand,
    required this.onStatusUpdate,
  });

  @override
  State<_StatusUpdateDialog> createState() => _StatusUpdateDialogState();
}

class _StatusUpdateDialogState extends State<_StatusUpdateDialog> {
  late ErrandStatus _newStatus;
  final _noteController = TextEditingController();
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _newStatus = _getNextStatus(widget.errand.status);
  }

  @override
  void dispose() {
    _noteController.dispose();
    super.dispose();
  }

  ErrandStatus _getNextStatus(ErrandStatus currentStatus) {
    switch (currentStatus) {
      case ErrandStatus.accepted:
        return ErrandStatus.inProgress;
      case ErrandStatus.inProgress:
        return ErrandStatus.completed;
      default:
        return currentStatus;
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Update Job Status'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Update status to: ${_getStatusText(_newStatus)}'),
          const SizedBox(height: 16),
          TextField(
            controller: _noteController,
            decoration: const InputDecoration(
              labelText: 'Add a note (optional)',
              border: OutlineInputBorder(),
            ),
            maxLines: 3,
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : () => _handleUpdate(),
          child: _isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('Update'),
        ),
      ],
    );
  }

  String _getStatusText(ErrandStatus status) {
    switch (status) {
      case ErrandStatus.accepted:
        return 'Accepted';
      case ErrandStatus.inProgress:
        return 'In Progress';
      case ErrandStatus.completed:
        return 'Completed';
      default:
        return 'Unknown';
    }
  }

  void _handleUpdate() async {
    setState(() {
      _isLoading = true;
    });

    await widget.onStatusUpdate(_newStatus, _noteController.text.trim());

    setState(() {
      _isLoading = false;
    });
  }
}

// Extended Active Errand Model
class ActiveErrand extends Errand {
  final String customerName;
  final String customerPhone;
  final String? customerAvatar;
  final Duration estimatedDuration;
  final String specialInstructions;
  final double customerRating;
  final double distanceFromRunner;
  final DateTime? completedDate;
  final List<ErrandStatusUpdate> statusHistory;

  ActiveErrand({
    required super.id,
    required super.title,
    required super.description,
    required super.payment,
    required super.location,
    super.customerId,
    super.runnerId,
    required super.status,
    required super.scheduledDate,
    required super.createdAt,
    required super.updatedAt,
    super.notes,
    required this.customerName,
    required this.customerPhone,
    this.customerAvatar,
    required this.estimatedDuration,
    required this.specialInstructions,
    required this.customerRating,
    required this.distanceFromRunner,
    this.completedDate,
    required this.statusHistory,
  });

  ActiveErrand copyWith({
    ErrandStatus? status,
    DateTime? completedDate,
  }) {
    return ActiveErrand(
      id: id,
      title: title,
      description: description,
      payment: payment,
      location: location,
      customerId: customerId,
      runnerId: runnerId,
      status: status ?? this.status,
      scheduledDate: scheduledDate,
      createdAt: createdAt,
      updatedAt: DateTime.now(),
      notes: notes,
      customerName: customerName,
      customerPhone: customerPhone,
      customerAvatar: customerAvatar,
      estimatedDuration: estimatedDuration,
      specialInstructions: specialInstructions,
      customerRating: customerRating,
      distanceFromRunner: distanceFromRunner,
      completedDate: completedDate ?? this.completedDate,
      statusHistory: statusHistory,
    );
  }
}

class ErrandStatusUpdate {
  final ErrandStatus status;
  final DateTime timestamp;
  final String note;

  ErrandStatusUpdate({
    required this.status,
    required this.timestamp,
    required this.note,
  });
}