import 'dart:math';

class LocationData {
  final double latitude;
  final double longitude;
  final String? address;
  final DateTime? timestamp;

  LocationData({
    required this.latitude,
    required this.longitude,
    this.address,
    this.timestamp,
  });

  factory LocationData.fromJson(Map<String, dynamic> json) {
    return LocationData(
      latitude: json['latitude']?.toDouble() ?? 0.0,
      longitude: json['longitude']?.toDouble() ?? 0.0,
      address: json['address'],
      timestamp: json['timestamp'] != null 
          ? DateTime.parse(json['timestamp'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'latitude': latitude,
      'longitude': longitude,
      'address': address,
      'timestamp': timestamp?.toIso8601String(),
    };
  }

  LocationData copyWith({
    double? latitude,
    double? longitude,
    String? address,
    DateTime? timestamp,
  }) {
    return LocationData(
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      address: address ?? this.address,
      timestamp: timestamp ?? this.timestamp,
    );
  }

  @override
  String toString() {
    return 'LocationData(lat: $latitude, lng: $longitude, address: $address)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LocationData &&
        other.latitude == latitude &&
        other.longitude == longitude &&
        other.address == address;
  }

  @override
  int get hashCode {
    return latitude.hashCode ^ longitude.hashCode ^ address.hashCode;
  }
}

class LocationUpdate {
  final String errandId;
  final LocationData location;
  final DateTime timestamp;
  final double speed; // km/h
  final double heading; // degrees from north
  final double accuracy; // meters
  final bool isMoving;
  final LocationStatus status;

  LocationUpdate({
    required this.errandId,
    required this.location,
    required this.timestamp,
    this.speed = 0.0,
    this.heading = 0.0,
    this.accuracy = 0.0,
    this.isMoving = false,
    this.status = LocationStatus.active,
  });

  factory LocationUpdate.fromJson(Map<String, dynamic> json) {
    return LocationUpdate(
      errandId: json['errandId'] ?? '',
      location: LocationData.fromJson(json['location'] ?? {}),
      timestamp: DateTime.parse(json['timestamp']),
      speed: json['speed']?.toDouble() ?? 0.0,
      heading: json['heading']?.toDouble() ?? 0.0,
      accuracy: json['accuracy']?.toDouble() ?? 0.0,
      isMoving: json['isMoving'] ?? false,
      status: LocationStatus.values.firstWhere(
        (status) => status.toString().split('.').last == json['status'],
        orElse: () => LocationStatus.active,
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'errandId': errandId,
      'location': location.toJson(),
      'timestamp': timestamp.toIso8601String(),
      'speed': speed,
      'heading': heading,
      'accuracy': accuracy,
      'isMoving': isMoving,
      'status': status.toString().split('.').last,
    };
  }

  LocationUpdate copyWith({
    String? errandId,
    LocationData? location,
    DateTime? timestamp,
    double? speed,
    double? heading,
    double? accuracy,
    bool? isMoving,
    LocationStatus? status,
  }) {
    return LocationUpdate(
      errandId: errandId ?? this.errandId,
      location: location ?? this.location,
      timestamp: timestamp ?? this.timestamp,
      speed: speed ?? this.speed,
      heading: heading ?? this.heading,
      accuracy: accuracy ?? this.accuracy,
      isMoving: isMoving ?? this.isMoving,
      status: status ?? this.status,
    );
  }

  bool get isAccurate => accuracy <= 10.0;
  bool get isRecentUpdate => DateTime.now().difference(timestamp).inMinutes < 5;
  
  String get formattedSpeed {
    if (speed < 1) return 'Stationary';
    return '${speed.toStringAsFixed(0)} km/h';
  }

  String get formattedHeading {
    if (heading >= 337.5 || heading < 22.5) return 'N';
    if (heading >= 22.5 && heading < 67.5) return 'NE';
    if (heading >= 67.5 && heading < 112.5) return 'E';
    if (heading >= 112.5 && heading < 157.5) return 'SE';
    if (heading >= 157.5 && heading < 202.5) return 'S';
    if (heading >= 202.5 && heading < 247.5) return 'SW';
    if (heading >= 247.5 && heading < 292.5) return 'W';
    if (heading >= 292.5 && heading < 337.5) return 'NW';
    return 'Unknown';
  }
}

enum LocationStatus {
  active,
  inactive,
  error,
  permissionDenied,
  serviceDisabled,
}

class GeofenceRegion {
  final String id;
  final LocationData center;
  final double radiusInMeters;
  final String name;
  final GeofenceType type;

  GeofenceRegion({
    required this.id,
    required this.center,
    required this.radiusInMeters,
    required this.name,
    required this.type,
  });

  factory GeofenceRegion.fromJson(Map<String, dynamic> json) {
    return GeofenceRegion(
      id: json['id'],
      center: LocationData.fromJson(json['center']),
      radiusInMeters: json['radiusInMeters']?.toDouble() ?? 0.0,
      name: json['name'],
      type: GeofenceType.values.firstWhere(
        (type) => type.toString().split('.').last == json['type'],
        orElse: () => GeofenceType.circular,
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'center': center.toJson(),
      'radiusInMeters': radiusInMeters,
      'name': name,
      'type': type.toString().split('.').last,
    };
  }

  bool containsLocation(LocationData location) {
    final distance = _calculateDistance(center, location);
    return distance <= (radiusInMeters / 1000); // Convert to km
  }

  double _calculateDistance(LocationData from, LocationData to) {
    const double earthRadiusKm = 6371;
    final double lat1Rad = from.latitude * (3.14159 / 180);
    final double lat2Rad = to.latitude * (3.14159 / 180);
    final double deltaLatRad = (to.latitude - from.latitude) * (3.14159 / 180);
    final double deltaLngRad = (to.longitude - from.longitude) * (3.14159 / 180);

    final double a = (deltaLatRad / 2) * (deltaLatRad / 2) +
        lat1Rad.cos() * lat2Rad.cos() *
        (deltaLngRad / 2) * (deltaLngRad / 2);
    
    final double c = 2 * a.sqrt().asin();
    return earthRadiusKm * c;
  }
}

enum GeofenceType {
  circular,
  rectangular,
}

class GeofenceEvent {
  final String geofenceId;
  final String errandId;
  final GeofenceEventType eventType;
  final LocationData location;
  final DateTime timestamp;

  GeofenceEvent({
    required this.geofenceId,
    required this.errandId,
    required this.eventType,
    required this.location,
    required this.timestamp,
  });

  factory GeofenceEvent.fromJson(Map<String, dynamic> json) {
    return GeofenceEvent(
      geofenceId: json['geofenceId'],
      errandId: json['errandId'],
      eventType: GeofenceEventType.values.firstWhere(
        (type) => type.toString().split('.').last == json['eventType'],
      ),
      location: LocationData.fromJson(json['location']),
      timestamp: DateTime.parse(json['timestamp']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'geofenceId': geofenceId,
      'errandId': errandId,
      'eventType': eventType.toString().split('.').last,
      'location': location.toJson(),
      'timestamp': timestamp.toIso8601String(),
    };
  }
}

enum GeofenceEventType {
  enter,
  exit,
  dwell,
}

class TrackingSession {
  final String id;
  final String errandId;
  final String runnerId;
  final DateTime startTime;
  final DateTime? endTime;
  final TrackingStatus status;
  final List<LocationUpdate> locationHistory;
  final double totalDistance; // kilometers
  final Duration totalDuration;

  TrackingSession({
    required this.id,
    required this.errandId,
    required this.runnerId,
    required this.startTime,
    this.endTime,
    required this.status,
    this.locationHistory = const [],
    this.totalDistance = 0.0,
    this.totalDuration = Duration.zero,
  });

  factory TrackingSession.fromJson(Map<String, dynamic> json) {
    return TrackingSession(
      id: json['id'],
      errandId: json['errandId'],
      runnerId: json['runnerId'],
      startTime: DateTime.parse(json['startTime']),
      endTime: json['endTime'] != null ? DateTime.parse(json['endTime']) : null,
      status: TrackingStatus.values.firstWhere(
        (status) => status.toString().split('.').last == json['status'],
      ),
      locationHistory: (json['locationHistory'] as List<dynamic>?)
          ?.map((location) => LocationUpdate.fromJson(location))
          .toList() ?? [],
      totalDistance: json['totalDistance']?.toDouble() ?? 0.0,
      totalDuration: Duration(seconds: json['totalDurationSeconds'] ?? 0),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'errandId': errandId,
      'runnerId': runnerId,
      'startTime': startTime.toIso8601String(),
      'endTime': endTime?.toIso8601String(),
      'status': status.toString().split('.').last,
      'locationHistory': locationHistory.map((location) => location.toJson()).toList(),
      'totalDistance': totalDistance,
      'totalDurationSeconds': totalDuration.inSeconds,
    };
  }

  bool get isActive => status == TrackingStatus.active;
  bool get isCompleted => status == TrackingStatus.completed;
  
  LocationUpdate? get currentLocation {
    if (locationHistory.isEmpty) return null;
    return locationHistory.last;
  }

  double get averageSpeed {
    if (locationHistory.isEmpty || totalDuration.inHours == 0) return 0.0;
    return totalDistance / totalDuration.inHours;
  }

  String get formattedDistance {
    if (totalDistance < 1) {
      return '${(totalDistance * 1000).round()} m';
    }
    return '${totalDistance.toStringAsFixed(1)} km';
  }

  String get formattedDuration {
    final hours = totalDuration.inHours;
    final minutes = totalDuration.inMinutes % 60;
    
    if (hours > 0) {
      return '${hours}h ${minutes}m';
    }
    return '${minutes}m';
  }
}

enum TrackingStatus {
  active,
  paused,
  completed,
  cancelled,
  error,
}